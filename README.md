<img src="https://og.seykim.dev/api/generate?heading=vite-react-tailwind-starter&text=React+vite+template+with+TailwindCSS+and+TypeScript+setup.&template=color&center=true&height=330" />

This is a project bootstrapped with [`@vitejs/app`](https://vitejs.dev/guide/#scaffolding-your-first-vite-project) (`react-ts`), added with [TailwindCSS](https://tailwindcss.com) and [TypeScript](https://www.typescriptlang.org) setup.

- ⚡ blazing fast dev server and build
- 🔗 route management added (`TanStack Router` - File Based routing)

[**Live Demo**](https://vite-react-tailwind-starter.seykim.dev/)

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/import/git?s=https://github.com/seykim/vite-react-tailwind-starter) [![Deploy to Netlify](https://www.netlify.com/img/deploy/button.svg)](https://app.netlify.com/start/deploy?repository=https://github.com/seykim/vite-react-tailwind-starter)

[![Open in StackBlitz](https://developer.stackblitz.com/img/open_in_stackblitz.svg)](https://stackblitz.com/github/seykim/vite-react-tailwind-starter)

## Getting Started

You can either click [`Use this template`](https://github.com/seykim/vite-react-tailwind-starter/generate) button on this repository and clone the repo or use npx degit like so:

```bash
npx degit seykim/vite-react-tailwind-starter <app_name>
```

```
pnpm i
```

Then, run the development server:

```bash
pnpm dev
```

## Testing

This project includes both unit tests (Vitest) and end-to-end tests (Playwright).

### Unit Tests
```bash
pnpm test              # Run unit tests
pnpm test:ui           # Run unit tests with UI
pnpm test:coverage     # Run unit tests with coverage
```

### End-to-End Tests
```bash
pnpm test:e2e          # Run all e2e tests (fast, Chromium only)
pnpm test:e2e:ui       # Run e2e tests with UI
pnpm test:e2e:headed   # Run e2e tests in headed mode
pnpm test:e2e:debug    # Debug e2e tests
```

The e2e tests use Playwright and include:
- Basic functionality tests (homepage, navigation, quiz link)
- Application feature tests (branding, theme toggle, 404 handling)
- Quiz functionality tests (navigation, interface, completion)

## Deployment

- build command: `pnpm build`
- output directory: `dist`

### Vercel

- https://vercel.com/docs/frameworks/vite

### Netlify

- https://docs.netlify.com/frameworks/vite/

## References

- [vite](https://vitejs.dev)
  - [avoid manual import](https://vitejs.dev/guide/features.html#jsx)
- [TailwindCSS](https://tailwindcss.com/)
- [TypeScript](https://www.typescriptlang.org)
