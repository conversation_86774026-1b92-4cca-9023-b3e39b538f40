name: Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '22'
        
    - name: Setup pnpm
      uses: pnpm/action-setup@v4
      with:
        version: 10.7.1
        
    - name: Install dependencies
      run: pnpm install
      
    - name: Run type check
      run: pnpm type:check
      
    - name: Run linting
      run: pnpm biome:ci
      
    - name: Run unit tests
      run: pnpm test

  e2e-tests:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '22'
        
    - name: Setup pnpm
      uses: pnpm/action-setup@v4
      with:
        version: 10.7.1
        
    - name: Install dependencies
      run: pnpm install
      
    - name: Install Playwright browsers
      run: pnpm exec playwright install --with-deps
      
    - name: Run e2e tests
      run: pnpm test:e2e:basic
      
    - name: Upload test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: playwright-report
        path: playwright-report/
        retention-days: 30
