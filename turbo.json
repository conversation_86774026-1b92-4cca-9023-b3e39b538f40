{"$schema": "https://turbo.build/schema.json", "tasks": {"type:check": {"inputs": ["tsconfig.json", "src/**"], "outputs": ["*.tsbuildinfo"], "env": ["VITE_API_BASE_URL"]}, "biome:check": {"outputs": [], "inputs": ["biome.json", "src/**"]}, "biome:ci": {"outputs": [], "inputs": ["biome.json", "src/**"]}, "build": {"dependsOn": ["type:check"], "outputs": ["dist/**", "public/**"], "env": ["VITE_API_BASE_URL"]}, "test": {"outputs": ["node_modules/.vite/**/results.json"], "inputs": ["vitest.config.ts", "src/**"]}, "test:e2e": {"outputs": ["playwright-report/**", "test-results/**"], "inputs": ["playwright.config.ts", "e2e/**"]}}}