import { createFileRoute, Outlet, useLocation } from '@tanstack/react-router';

import Quiz from '@/lib/pages/quiz';

const QuizLayout = () => {
  const location = useLocation();

  // If we're on the exact /quiz path, show the interactive quiz
  // If we're on a child route like /quiz/preview, show the outlet
  if (location.pathname === '/quiz') {
    return <Quiz />;
  }

  return <Outlet />;
};

export const Route = createFileRoute('/quiz')({
  component: QuizLayout,
});
