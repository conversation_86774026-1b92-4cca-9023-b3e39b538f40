/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as QuizRouteImport } from './routes/quiz'
import { Route as IndexRouteImport } from './routes/index'
import { Route as QuizPreviewRouteImport } from './routes/quiz.preview'

const QuizRoute = QuizRouteImport.update({
  id: '/quiz',
  path: '/quiz',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const QuizPreviewRoute = QuizPreviewRouteImport.update({
  id: '/preview',
  path: '/preview',
  getParentRoute: () => QuizRoute,
} as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/quiz': typeof QuizRouteWithChildren
  '/quiz/preview': typeof QuizPreviewRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/quiz': typeof QuizRouteWithChildren
  '/quiz/preview': typeof QuizPreviewRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/quiz': typeof QuizRouteWithChildren
  '/quiz/preview': typeof QuizPreviewRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths: '/' | '/quiz' | '/quiz/preview'
  fileRoutesByTo: FileRoutesByTo
  to: '/' | '/quiz' | '/quiz/preview'
  id: '__root__' | '/' | '/quiz' | '/quiz/preview'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  QuizRoute: typeof QuizRouteWithChildren
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/quiz': {
      id: '/quiz'
      path: '/quiz'
      fullPath: '/quiz'
      preLoaderRoute: typeof QuizRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/quiz/preview': {
      id: '/quiz/preview'
      path: '/preview'
      fullPath: '/quiz/preview'
      preLoaderRoute: typeof QuizPreviewRouteImport
      parentRoute: typeof QuizRoute
    }
  }
}

interface QuizRouteChildren {
  QuizPreviewRoute: typeof QuizPreviewRoute
}

const QuizRouteChildren: QuizRouteChildren = {
  QuizPreviewRoute: QuizPreviewRoute,
}

const QuizRouteWithChildren = QuizRoute._addFileChildren(QuizRouteChildren)

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  QuizRoute: QuizRouteWithChildren,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
