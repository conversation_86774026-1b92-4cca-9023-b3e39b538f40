import type { QuizQuestionData } from '../components/quiz-question';

export const quizQuestions: QuizQuestionData[] = [
  {
    id: 'q1',
    question: 'What is the correct way to define a functional component in React with TypeScript?',
    options: [
      {
        id: 'q1-a',
        text: 'const MyComponent = () => { return <div>Hello</div>; };',
        isCorrect: true,
      },
      {
        id: 'q1-b',
        text: 'function MyComponent() { return <div>Hello</div>; }',
        isCorrect: false,
      },
      {
        id: 'q1-c',
        text: 'const MyComponent: React.FC = () => <div>Hello</div>;',
        isCorrect: false,
      },
      {
        id: 'q1-d',
        text: 'All of the above are correct',
        isCorrect: false,
      },
    ],
    explanation: 'While all options can work, the arrow function syntax without React.FC is the most modern and recommended approach in current React with TypeScript.',
  },
  {
    id: 'q2',
    question: 'Which hook is used to manage state in functional components?',
    options: [
      {
        id: 'q2-a',
        text: 'useEffect',
        isCorrect: false,
      },
      {
        id: 'q2-b',
        text: 'useState',
        isCorrect: true,
      },
      {
        id: 'q2-c',
        text: 'useContext',
        isCorrect: false,
      },
      {
        id: 'q2-d',
        text: 'useReducer',
        isCorrect: false,
      },
    ],
    explanation: 'useState is the primary hook for managing local state in functional components.',
  },
  {
    id: 'q3',
    question: 'What is the purpose of the useEffect hook?',
    options: [
      {
        id: 'q3-a',
        text: 'To manage component state',
        isCorrect: false,
      },
      {
        id: 'q3-b',
        text: 'To handle side effects and lifecycle events',
        isCorrect: true,
      },
      {
        id: 'q3-c',
        text: 'To create context providers',
        isCorrect: false,
      },
      {
        id: 'q3-d',
        text: 'To optimize component rendering',
        isCorrect: false,
      },
    ],
    explanation: 'useEffect is used for side effects like API calls, subscriptions, and manually changing the DOM, replacing lifecycle methods from class components.',
  },
  {
    id: 'q4',
    question: 'In TypeScript, what is the correct way to type a component props interface?',
    options: [
      {
        id: 'q4-a',
        text: 'type Props = { name: string; age: number; };',
        isCorrect: false,
      },
      {
        id: 'q4-b',
        text: 'interface Props { name: string; age: number; }',
        isCorrect: true,
      },
      {
        id: 'q4-c',
        text: 'const Props = { name: string, age: number };',
        isCorrect: false,
      },
      {
        id: 'q4-d',
        text: 'Both A and B are correct',
        isCorrect: false,
      },
    ],
    explanation: 'While both type and interface can work, interface is generally preferred for component props as it can be extended and has better error messages.',
  },
  {
    id: 'q5',
    question: 'What is the correct way to handle events in React with TypeScript?',
    options: [
      {
        id: 'q5-a',
        text: 'const handleClick = (event: Event) => { ... };',
        isCorrect: false,
      },
      {
        id: 'q5-b',
        text: 'const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => { ... };',
        isCorrect: true,
      },
      {
        id: 'q5-c',
        text: 'const handleClick = (event: MouseEvent) => { ... };',
        isCorrect: false,
      },
      {
        id: 'q5-d',
        text: 'const handleClick = (event: any) => { ... };',
        isCorrect: false,
      },
    ],
    explanation: 'React.MouseEvent<HTMLButtonElement> provides the correct typing for mouse events on button elements, giving you access to React-specific event properties.',
  },
];
