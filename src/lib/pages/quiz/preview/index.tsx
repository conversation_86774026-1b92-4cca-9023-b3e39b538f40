import { PDFDownloadLink, Document, Page, Text, View, StyleSheet } from '@react-pdf/renderer';

import { Button } from '@/components/ui/button';
import { quizQuestions } from '@/lib/pages/quiz/data/quiz-data';
import type { QuizQuestionData, QuizOption } from '../components/quiz-question';

const styles = StyleSheet.create({
  page: {
    padding: 30,
    fontFamily: 'Helvetica',
  },
  title: {
    fontSize: 24,
    textAlign: 'center',
    marginBottom: 20,
    fontFamily: 'Helvetica-Bold',
  },
  questionContainer: {
    marginBottom: 15,
  },
  questionText: {
    fontSize: 14,
    marginBottom: 10,
    fontFamily: 'Helvetica-Bold',
  },
  optionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 5,
    marginLeft: 10,
  },
  optionText: {
    fontSize: 12,
    marginLeft: 5,
  },
  answersSection: {
    marginTop: 30,
    borderTopWidth: 1,
    borderTopColor: '#cccccc',
    paddingTop: 20,
  },
  answerKeyTitle: {
    fontSize: 18,
    textAlign: 'center',
    marginBottom: 15,
    fontFamily: 'Helvetica-Bold',
  },
  answerContainer: {
    flexDirection: 'row',
    marginBottom: 5,
  },
  answerQuestion: {
    fontSize: 12,
    fontFamily: 'Helvetica-Bold',
    marginRight: 5,
  },
  answerText: {
    fontSize: 12,
  },
});

const QuizDocument = () => (
  <Document>
    <Page style={styles.page}>
      <Text style={styles.title}>Quiz Questions</Text>
      {quizQuestions.map((question: QuizQuestionData, index: number) => (
        <View key={question.id} style={styles.questionContainer}>
          <Text style={styles.questionText}>{`${index + 1}. ${question.question}`}</Text>
          {question.options.map((option: QuizOption, optionIndex: number) => (
            <View key={option.id} style={styles.optionContainer}>
              <Text style={styles.optionText}>{`${String.fromCharCode(65 + optionIndex)}. ${option.text}`}</Text>
            </View>
          ))}
        </View>
      ))}
    </Page>
    <Page style={styles.page}>
      <View style={styles.answersSection}>
        <Text style={styles.answerKeyTitle}>Answer Key</Text>
        {quizQuestions.map((question: QuizQuestionData, index: number) => {
          const correctOption = question.options.find((opt: QuizOption) => opt.isCorrect);
          const correctOptionLetter = String.fromCharCode(65 + question.options.indexOf(correctOption!));
          return (
            <View key={question.id} style={styles.answerContainer}>
              <Text style={styles.answerQuestion}>{`${index + 1}.`}</Text>
              <Text style={styles.answerText}>{`${correctOptionLetter}. ${correctOption?.text}`}</Text>
            </View>
          );
        })}
      </View>
    </Page>
  </Document>
);

export const QuizPreviewPage = () => {
  return (
    <div className="min-h-screen bg-white dark:bg-gray-900 p-8 print:p-4">
      {/* Header with print controls */}
      <div className="flex justify-between items-center mb-8 print:hidden">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Quiz Preview</h1>
        <div className="flex gap-4">
          <Button
            onClick={() => window.print()}
            variant="default"
            size="lg"
          >
            Print Quiz
          </Button>
          <PDFDownloadLink document={<QuizDocument />} fileName="quiz.pdf">
            {({ loading }) => (
              <Button size="lg" variant="secondary" disabled={loading}>
                {loading ? 'Generating PDF...' : 'Download PDF'}
              </Button>
            )}
          </PDFDownloadLink>
        </div>
      </div>

      {/* Printable quiz content */}
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white print:text-black mb-2">
            Quiz Questions
          </h2>
          <p className="text-gray-600 dark:text-gray-300 print:text-gray-700">
            Answer all questions. Choose the best answer for each question.
          </p>
        </div>

        {/* Questions */}
        <div className="space-y-8">
          {quizQuestions.map((question, index) => (
            <div key={question.id} className="quiz-question border-b border-gray-200 dark:border-gray-700 print:border-gray-300 pb-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white print:text-black mb-4">
                {index + 1}. {question.question}
              </h3>

              <div className="space-y-3 ml-4">
                {question.options.map((option, optionIndex) => (
                  <div key={option.id} className="flex items-start gap-3">
                    <div className="w-6 h-6 border-2 border-gray-300 dark:border-gray-600 print:border-gray-400 rounded mt-0.5 flex-shrink-0" />
                    <label className="text-gray-700 dark:text-gray-300 print:text-black leading-relaxed">
                      <span className="font-medium mr-2">
                        {String.fromCharCode(65 + optionIndex)}.
                      </span>
                      {option.text}
                    </label>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* Footer for print */}
        <div className="mt-12 pt-8 border-t border-gray-200 dark:border-gray-700 print:border-gray-300 text-center print:block">
          <p className="text-sm text-gray-500 dark:text-gray-400 print:text-gray-600">
            End of Quiz - Please review your answers before submitting
          </p>
        </div>
      </div>
    </div>
  );
};
