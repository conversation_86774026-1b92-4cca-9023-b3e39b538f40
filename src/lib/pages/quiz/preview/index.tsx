import { PDFDownloadLink, Document, Page, Text, View, StyleSheet } from '@react-pdf/renderer';

import { Button } from '@/components/ui/button';
import { quizQuestions } from '@/lib/pages/quiz/data/quiz-data';
import type { QuizQuestionData, QuizOption } from '../components/quiz-question';

const styles = StyleSheet.create({
  page: {
    padding: 30,
    fontFamily: 'Helvetica',
  },
  title: {
    fontSize: 24,
    textAlign: 'center',
    marginBottom: 20,
    fontFamily: 'Helvetica-Bold',
  },
  questionContainer: {
    marginBottom: 15,
  },
  questionText: {
    fontSize: 14,
    marginBottom: 10,
    fontFamily: 'Helvetica-Bold',
  },
  optionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 5,
    marginLeft: 10,
  },
  optionText: {
    fontSize: 12,
    marginLeft: 5,
  },
  answersSection: {
    marginTop: 30,
    borderTopWidth: 1,
    borderTopColor: '#cccccc',
    paddingTop: 20,
  },
  answerKeyTitle: {
    fontSize: 18,
    textAlign: 'center',
    marginBottom: 15,
    fontFamily: 'Helvetica-Bold',
  },
  answerContainer: {
    flexDirection: 'row',
    marginBottom: 5,
  },
  answerQuestion: {
    fontSize: 12,
    fontFamily: 'Helvetica-Bold',
    marginRight: 5,
  },
  answerText: {
    fontSize: 12,
  },
});

const QuizDocument = () => (
  <Document>
    <Page style={styles.page}>
      <Text style={styles.title}>Quiz Questions</Text>
      {quizQuestions.map((question: QuizQuestionData, index: number) => (
        <View key={question.id} style={styles.questionContainer}>
          <Text style={styles.questionText}>{`${index + 1}. ${question.question}`}</Text>
          {question.options.map((option: QuizOption, optionIndex: number) => (
            <View key={option.id} style={styles.optionContainer}>
              <Text style={styles.optionText}>{`${String.fromCharCode(65 + optionIndex)}. ${option.text}`}</Text>
            </View>
          ))}
        </View>
      ))}
    </Page>
    <Page style={styles.page}>
      <View style={styles.answersSection}>
        <Text style={styles.answerKeyTitle}>Answer Key</Text>
        {quizQuestions.map((question: QuizQuestionData, index: number) => {
          const correctOption = question.options.find((opt: QuizOption) => opt.isCorrect);
          const correctOptionLetter = String.fromCharCode(65 + question.options.indexOf(correctOption!));
          return (
            <View key={question.id} style={styles.answerContainer}>
              <Text style={styles.answerQuestion}>{`${index + 1}.`}</Text>
              <Text style={styles.answerText}>{`${correctOptionLetter}. ${correctOption?.text}`}</Text>
            </View>
          );
        })}
      </View>
    </Page>
  </Document>
);

export const QuizPreviewPage = () => {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gray-50 dark:bg-gray-900 p-4">
      <h1 className="text-3xl font-bold mb-4 text-gray-900 dark:text-white">Quiz Preview & Print</h1>
      <p className="text-lg text-gray-600 dark:text-gray-300 mb-8">Click the button below to download a printable PDF of the quiz.</p>
      <PDFDownloadLink document={<QuizDocument />} fileName="quiz.pdf">
        {({ loading }) => (
          <Button size="lg" disabled={loading}>
            {loading ? 'Generating PDF...' : 'Download Quiz PDF'}
          </Button>
        )}
      </PDFDownloadLink>
    </div>
  );
};
