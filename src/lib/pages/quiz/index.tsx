import { useState } from 'react';

import { Button } from '@/components/ui/button';

import { QuizProgress } from './components/quiz-progress';
import { QuizQuestion } from './components/quiz-question';
import { QuizResults } from './components/quiz-results';
import { quizQuestions } from './data/quiz-data';

interface QuizAnswer {
  questionId: string;
  selectedOptionId: string;
  isCorrect: boolean;
}

const Quiz = () => {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState<QuizAnswer[]>([]);
  const [isQuizCompleted, setIsQuizCompleted] = useState(false);
  const [showExplanation, setShowExplanation] = useState(false);

  const currentQuestion = quizQuestions[currentQuestionIndex];
  const score = answers.filter(answer => answer.isCorrect).length;
  const isLastQuestion = currentQuestionIndex === quizQuestions.length - 1;

  const handleAnswer = (questionId: string, selectedOptionId: string, isCorrect: boolean) => {
    const newAnswer: QuizAnswer = {
      questionId,
      selectedOptionId,
      isCorrect,
    };

    setAnswers(prev => [...prev, newAnswer]);
    setShowExplanation(true);
  };

  const handleNextQuestion = () => {
    if (isLastQuestion) {
      setIsQuizCompleted(true);
    } else {
      setCurrentQuestionIndex(prev => prev + 1);
      setShowExplanation(false);
    }
  };

  const handleRestartQuiz = () => {
    setCurrentQuestionIndex(0);
    setAnswers([]);
    setIsQuizCompleted(false);
    setShowExplanation(false);
  };

  const getCurrentAnswer = () => {
    return answers.find(answer => answer.questionId === currentQuestion?.id);
  };

  if (isQuizCompleted) {
    return (
      <div className="min-h-[60vh] flex flex-col items-center justify-center p-4">
        <QuizResults
          score={score}
          totalQuestions={quizQuestions.length}
          onRestart={handleRestartQuiz}
        />
      </div>
    );
  }

  if (!currentQuestion) {
    return (
      <div className="min-h-[60vh] flex flex-col items-center justify-center p-4">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            Quiz not available
          </h2>
          <a
            href="/"
            className="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg transition-colors duration-200"
          >
            Back to Home
          </a>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-[60vh] flex flex-col items-center justify-start p-4 pt-8">
      <div className="w-full max-w-2xl mx-auto mb-4 flex justify-end">
        <Button
          variant="secondary"
          onClick={() => {}}
        >
          Download Quiz
        </Button>
      </div>

      <QuizProgress
        currentQuestion={currentQuestionIndex + 1}
        totalQuestions={quizQuestions.length}
        score={score}
        isCompleted={isQuizCompleted}
      />

      <QuizQuestion
        question={currentQuestion}
        onAnswer={handleAnswer}
        isAnswered={showExplanation}
        selectedOptionId={getCurrentAnswer()?.selectedOptionId}
      />

      {showExplanation && (
        <div className="w-full max-w-2xl mx-auto mt-6 text-center">
          <Button
            onClick={handleNextQuestion}
            data-testid="next-question-button"
            size="lg"
          >
            {isLastQuestion ? 'View Results' : 'Next Question'}
          </Button>
        </div>
      )}
    </div>
  );
};

export default Quiz;
