import { useState } from 'react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';

export interface QuizOption {
  id: string;
  text: string;
  isCorrect: boolean;
}

export interface QuizQuestionData {
  id: string;
  question: string;
  options: QuizOption[];
  explanation?: string;
}

interface QuizQuestionProps {
  question: QuizQuestionData;
  onAnswer: (questionId: string, selectedOptionId: string, isCorrect: boolean) => void;
  isAnswered: boolean;
  selectedOptionId?: string;
}

export const QuizQuestion = ({ question, onAnswer, isAnswered, selectedOptionId }: QuizQuestionProps) => {
  const [localSelectedId, setLocalSelectedId] = useState<string | undefined>(selectedOptionId);

  const handleOptionSelect = (optionId: string) => {
    if (isAnswered) return;
    
    setLocalSelectedId(optionId);
    const selectedOption = question.options.find(opt => opt.id === optionId);
    if (selectedOption) {
      onAnswer(question.id, optionId, selectedOption.isCorrect);
    }
  };

  const getOptionClassName = (option: QuizOption) => {
    const baseClasses = "w-full p-4 text-left border-2 rounded-lg transition-all duration-200 hover:bg-gray-50 dark:hover:bg-gray-800";
    
    if (!isAnswered) {
      return `${baseClasses} border-gray-200 dark:border-gray-700 cursor-pointer hover:border-blue-300 dark:hover:border-blue-600`;
    }
    
    if (option.id === localSelectedId) {
      if (option.isCorrect) {
        return `${baseClasses} border-green-500 bg-green-50 dark:bg-green-900/20 text-green-800 dark:text-green-200`;
      } else {
        return `${baseClasses} border-red-500 bg-red-50 dark:bg-red-900/20 text-red-800 dark:text-red-200`;
      }
    }
    
    if (option.isCorrect && isAnswered) {
      return `${baseClasses} border-green-500 bg-green-50 dark:bg-green-900/20 text-green-800 dark:text-green-200`;
    }
    
    return `${baseClasses} border-gray-200 dark:border-gray-700 opacity-60`;
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
          {question.question}
        </h3>
      </CardHeader>
      <CardContent>
      
        <div className="space-y-3">
          {question.options.map((option) => (
            <Button
              key={option.id}
              variant="outline"
              onClick={() => handleOptionSelect(option.id)}
              disabled={isAnswered}
              className={`w-full p-4 h-auto justify-start text-left ${getOptionClassName(option)}`}
              data-testid={`option-${option.id}`}
            >
              <span className="flex items-center">
                <span className="w-6 h-6 rounded-full border-2 border-current mr-3 flex items-center justify-center text-sm font-bold">
                  {String.fromCharCode(65 + question.options.indexOf(option))}
                </span>
                {option.text}
              </span>
            </Button>
          ))}
        </div>
      
        {isAnswered && question.explanation && (
          <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
            <h4 className="font-semibold text-blue-800 dark:text-blue-200 mb-2">Explanation:</h4>
            <p className="text-blue-700 dark:text-blue-300">{question.explanation}</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
