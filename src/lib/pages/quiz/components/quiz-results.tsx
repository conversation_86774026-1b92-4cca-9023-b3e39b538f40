import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

interface QuizResultsProps {
  score: number;
  totalQuestions: number;
  onRestart: () => void;
}

export const QuizResults = ({ score, totalQuestions, onRestart }: QuizResultsProps) => {
  const percentage = Math.round((score / totalQuestions) * 100);
  
  const getResultMessage = () => {
    if (percentage >= 90) return "Excellent! 🎉";
    if (percentage >= 70) return "Great job! 👏";
    if (percentage >= 50) return "Good effort! 👍";
    return "Keep practicing! 💪";
  };

  const getResultColor = () => {
    if (percentage >= 90) return "text-green-600 dark:text-green-400";
    if (percentage >= 70) return "text-blue-600 dark:text-blue-400";
    if (percentage >= 50) return "text-yellow-600 dark:text-yellow-400";
    return "text-red-600 dark:text-red-400";
  };

  return (
    <Card className="w-full max-w-2xl mx-auto text-center">
      <CardContent className="p-8">
        <div className="mb-6">
        <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
          Quiz Complete!
        </h2>
        
        <div className={`text-6xl font-bold mb-4 ${getResultColor()}`} data-testid="final-percentage">
          {percentage}%
        </div>
        
        <p className={`text-2xl font-semibold mb-2 ${getResultColor()}`}>
          {getResultMessage()}
        </p>
        
        <p className="text-lg text-gray-600 dark:text-gray-400" data-testid="score-summary">
          You scored {score} out of {totalQuestions} questions correctly.
        </p>
      </div>
      
        <div className="space-y-4">
          <Button
            onClick={onRestart}
            className="w-full"
            size="lg"
            data-testid="restart-quiz-button"
          >
            Take Quiz Again
          </Button>

          <Button
            variant="secondary"
            className="w-full"
            size="lg"
            asChild
          >
            <a href="/">
              Back to Home
            </a>
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
