import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';

interface QuizProgressProps {
  currentQuestion: number;
  totalQuestions: number;
  score: number;
  isCompleted: boolean;
}

export const QuizProgress = ({ currentQuestion, totalQuestions, score, isCompleted }: QuizProgressProps) => {
  const progressPercentage = (currentQuestion / totalQuestions) * 100;
  const scorePercentage = totalQuestions > 0 ? (score / totalQuestions) * 100 : 0;

  return (
    <div className="w-full max-w-2xl mx-auto mb-8">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
          React & TypeScript Quiz
        </h2>
        <div className="text-sm text-gray-600 dark:text-gray-400">
          {isCompleted ? (
            <Badge variant="secondary" data-testid="final-score">
              Final Score: {score}/{totalQuestions} ({Math.round(scorePercentage)}%)
            </Badge>
          ) : (
            <Badge variant="outline" data-testid="question-counter">
              Question {currentQuestion} of {totalQuestions}
            </Badge>
          )}
        </div>
      </div>
      
      <Progress
        value={progressPercentage}
        className="mb-4"
        data-testid="progress-bar"
      />
      
      {!isCompleted && (
        <div className="text-center text-sm text-gray-600 dark:text-gray-400">
          Current Score: {score}/{currentQuestion > 0 ? currentQuestion : totalQuestions}
        </div>
      )}
    </div>
  );
};
