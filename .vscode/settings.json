{"files.watcherExclude": {"**/routeTree.gen.ts": true}, "search.exclude": {"**/routeTree.gen.ts": true}, "files.readonlyInclude": {"**/routeTree.gen.ts": true}, "editor.defaultFormatter": "biomejs.biome", "typescript.tsdk": "node_modules/typescript/lib", "typescript.preferences.importModuleSpecifier": "non-relative", "tailwindCSS.experimental.classRegex": [["clsx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]], "[javascript]": {"editor.defaultFormatter": "biomejs.biome"}, "[typescriptreact]": {"editor.defaultFormatter": "biomejs.biome"}, "[typescript]": {"editor.defaultFormatter": "biomejs.biome"}, "[json]": {"editor.defaultFormatter": "biomejs.biome"}, "[html]": {"editor.defaultFormatter": "biomejs.biome"}, "[jsonc]": {"editor.defaultFormatter": "biomejs.biome"}, "[javascriptreact]": {"editor.defaultFormatter": "biomejs.biome"}, "[css]": {"editor.defaultFormatter": "biomejs.biome"}, "[markdown]": {"editor.defaultFormatter": "biomejs.biome"}, "[vue]": {"editor.defaultFormatter": "biomejs.biome"}, "[scss]": {"editor.defaultFormatter": "biomejs.biome"}, "[less]": {"editor.defaultFormatter": "biomejs.biome"}}