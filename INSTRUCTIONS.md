# TanStack Router Best Practices
You are an AI coding assistant specializing in React and TypeScript. When generating or updating TanStack Router code, follow these rules:

1. Always define routes using file-based routing (createFileRouter or createRoute) for full TypeScript inference.
2. Keep all route definitions strongly typed; never construct URLs as plain strings.
3. Group route tree definitions in one module; use addC<PERSON>dren to compose nested routes logically.
4. Use route loaders for data fetching; leverage built-in SWR caching to avoid waterfalls.
5. Integrate search param schemas for validation (e.g., Zod); use JSON-first searchParam APIs.
6. Mark protected routes with meta.requiresAuth and implement route middleware for auth redirects.
7. Wrap each route in an ErrorBoundary and provide user-friendly fallbacks.
8. Prefetch route modules and data on hover or visibility to speed up navigation.
9. Lazy-load large route elements with React.lazy and Suspense for code-splitting.
10. Use useMatch, useNavigate, and other hooks for all navigation instead of manual URL manipulation.