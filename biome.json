{"$schema": "https://biomejs.dev/schemas/2.0.0-beta.6/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": false}, "files": {"ignoreUnknown": false, "includes": ["src/**/*", "e2e/**/*", "!src/*.gen.ts", "!turbo", "!src/lib/components/ui/**/*", "*.config.ts", "!*.css"]}, "formatter": {"enabled": true, "indentStyle": "space"}, "linter": {"enabled": true, "rules": {"recommended": true, "a11y": {"useSemanticElements": "error"}, "complexity": {"noExcessiveCognitiveComplexity": "error", "noUselessStringConcat": "error", "noUselessUndefinedInitialization": "error", "useSimplifiedLogicExpression": "error", "noVoid": "warn"}, "correctness": {"noUnusedImports": "error", "noUnusedVariables": "error", "noUnusedFunctionParameters": "error", "useHookAtTopLevel": "error"}, "performance": {"noBarrelFile": "error", "useTopLevelRegex": "error"}, "style": {"noDefaultExport": "error", "useBlockStatements": "error", "useCollapsedElseIf": "error", "useDefaultSwitchClause": "error", "useConsistentArrayType": {"level": "error", "options": {"syntax": "generic"}}, "useFilenamingConvention": {"level": "error", "options": {"filenameCases": ["kebab-case"]}}}, "suspicious": {"noDuplicateElseIf": "error", "noConsole": {"level": "error", "options": {"allow": ["error", "info"]}}, "noEmptyBlockStatements": "error", "useAwait": "error"}}}, "javascript": {"formatter": {"quoteStyle": "single"}}, "overrides": [{"includes": ["src/lib/pages/**/index.tsx", "src/*.ts", "*.ts"], "linter": {"rules": {"style": {"noDefaultExport": "off"}}}}, {"includes": ["src/routes/**/*"], "linter": {"rules": {"style": {"useFilenamingConvention": "off"}}}}], "assist": {"enabled": true, "actions": {"source": {"organizeImports": {"level": "on", "options": {"groups": [[":URL:", ":NODE:", ":PACKAGE:"], ":BLANK_LINE:", [":ALIAS:"], ":BLANK_LINE:", [":PATH:"]]}}}}}}