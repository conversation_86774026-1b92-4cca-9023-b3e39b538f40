import { test, expect } from '@playwright/test';

test.describe('Basic Functionality', () => {
  test('should load the homepage with correct content', async ({ page }) => {
    await page.goto('/');

    // Check page title
    await expect(page).toHaveTitle('<PERSON>y Kim Vite React Tailwind Starter');

    // Check main heading is present
    const heading = page.getByRole('heading').first();
    await expect(heading).toBeVisible();
    await expect(heading).toContainText('seykim-vite-react-tailwind-starter');
  });

  test('should have quiz link and basic navigation', async ({ page }) => {
    await page.goto('/');

    // Check quiz link exists
    const quizLink = page.getByTestId('quiz-link');
    await expect(quizLink).toBeVisible();
    await expect(quizLink).toContainText('Take React Quiz');

    // Check external links have proper attributes
    const externalLinks = page.locator('a[target="_blank"]');
    const linkCount = await externalLinks.count();

    if (linkCount > 0) {
      const firstLink = externalLinks.first();
      await expect(firstLink).toHaveAttribute('target', '_blank');
    }
  });
});
