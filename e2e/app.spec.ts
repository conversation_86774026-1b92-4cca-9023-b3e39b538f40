import { test, expect } from '@playwright/test';

test.describe('Application Features', () => {
  test('should display correct branding and theme toggle', async ({ page }) => {
    await page.goto('/');

    // Check page title contains <PERSON><PERSON> branding
    await expect(page).toHaveTitle('<PERSON><PERSON> <PERSON> Vite React Tailwind Starter');

    // Check main heading contains the project name
    const heading = page.locator('h1').first();
    await expect(heading).toContainText('seykim-vite-react-tailwind-starter');

    // Test theme toggle is functional
    const themeToggle = page.locator('button').first();
    await expect(themeToggle).toBeVisible();
    await themeToggle.click({ force: true });
    await expect(themeToggle).toBeEnabled();
  });

  test('should handle 404 pages and have proper links', async ({ page }) => {
    // Test 404 page
    await page.goto('/non-existent-page');
    const notFoundHeading = page.getByRole('heading', { name: 'Page Not Found' });
    await expect(notFoundHeading).toBeVisible();

    // Go back to home and check GitHub links
    await page.goto('/');
    const templateButton = page.getByRole('link', { name: 'Use This Template' });
    if (await templateButton.count() > 0) {
      await expect(templateButton).toHaveAttribute('href');
    }
  });
});
