import { test, expect } from '@playwright/test';

test.describe('Quiz Functionality', () => {
  test('should navigate to quiz and display basic interface', async ({ page }) => {
    // Navigate directly to quiz page
    await page.goto('/quiz');

    // Check quiz interface loads
    const title = page.getByRole('heading', { name: 'React & TypeScript Quiz' });
    await expect(title).toBeVisible();

    // Check question counter and progress
    const questionCounter = page.getByTestId('question-counter');
    await expect(questionCounter).toContainText('Question 1 of 5');

    const progressBar = page.getByTestId('progress-bar');
    await expect(progressBar).toBeVisible();

    // Check options are available
    const options = page.locator('[data-testid^="option-"]');
    await expect(options).toHaveCount(4);
  });

  test('should allow clicking quiz options', async ({ page }) => {
    await page.goto('/quiz');

    // Check that options are clickable
    const firstOption = page.locator('[data-testid^="option-"]').first();
    await expect(firstOption).toBeVisible();
    await firstOption.click({ force: true });

    // Just verify the option is still there after clicking (basic interaction test)
    await expect(firstOption).toBeVisible();
  });

  test('should have quiz link on home page', async ({ page }) => {
    await page.goto('/');

    // Check quiz link exists
    const quizLink = page.getByTestId('quiz-link');
    await expect(quizLink).toBeVisible();
    await expect(quizLink).toContainText('Take React Quiz');
    await expect(quizLink).toHaveAttribute('href', '/quiz');
  });
});
