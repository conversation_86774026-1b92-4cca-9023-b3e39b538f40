# End-to-End Tests

This directory contains fast, focused Playwright-based end-to-end tests for the application.

## Test Files

### `basic.spec.ts`
Core functionality tests that verify:
- Homepage loading with correct content
- Quiz link presence and navigation
- External links have proper attributes

### `app.spec.ts`
Application-specific feature tests that verify:
- Correct branding and content (<PERSON><PERSON> references)
- Theme toggle functionality
- 404 page handling
- Basic link validation

### `quiz.spec.ts`
Quiz functionality tests that verify:
- Navigation from home to quiz
- Quiz interface displays correctly
- Question answering and progression
- Results page and navigation back

## Running Tests

```bash
# Run all tests (fast, Chromium only)
pnpm test:e2e

# Run with UI for debugging
pnpm test:e2e:ui

# Run in headed mode (see browser)
pnpm test:e2e:headed

# Debug specific test
pnpm test:e2e:debug
```

## Configuration

The tests are configured in `playwright.config.ts` with:
- Tests run against `http://localhost:3000`
- Automatic dev server startup
- Chromium only (for speed)
- HTML reporter for results
- Trace collection on retry

## Design Principles

These tests are designed to be:
- **Fast**: Chromium only, minimal waits, focused assertions
- **Stable**: Simple selectors, avoid flaky interactions
- **Focused**: Test core functionality, not implementation details
- **Maintainable**: Clear test names, minimal setup/teardown

## Best Practices

- Keep tests simple and focused on user-visible behavior
- Use data-testid attributes for reliable element selection
- Avoid testing implementation details that change frequently
- Test the happy path and critical error scenarios only
