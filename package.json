{"name": "vite-react-tailwind-v2", "private": true, "type": "module", "author": "<PERSON><PERSON>", "homepage": "https://github.com/seykim/vite-react-tailwind-starter", "repository": {"type": "git", "url": "https://github.com/seykim/vite-react-tailwind-starter.git"}, "engines": {"node": ">=22.16.x"}, "packageManager": "pnpm@10.7.1", "pnpm": {"onlyBuiltDependencies": ["@tailwindcss/oxide", "sharp"]}, "scripts": {"dev": "vite --port 3000", "start": "vite --port 3000", "build": "vite build", "serve": "vite preview", "generate-pwa-assets": "pwa-assets-generator", "turbo": "pnpm dlx turbo@2.5.0", "type:check": "tsc", "test": "vitest run", "test:ui": "vitest --ui --coverage", "test:coverage": "vitest --coverage run", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "biome:check": "biome check", "biome:fix": "pnpm biome:check --write", "biome:ci": "biome ci", "check:turbo": "pnpm turbo biome:check type:check test", "release": "cross-env HUSKY=0 commit-and-tag-version", "up-interactive": "pnpm up -i", "up-latest": "pnpm up-interactive -L", "prepare": "husky"}, "dependencies": {"@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@react-pdf/renderer": "^4.3.0", "@tailwindcss/vite": "^4.0.6", "@tanstack/react-router": "^1.121.0", "@tanstack/react-router-devtools": "^1.121.0", "@tanstack/router-plugin": "^1.121.0", "@theme-toggles/react": "^4.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.525.0", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.0.6", "zod": "^3.25.61"}, "devDependencies": {"@biomejs/biome": "2.0.0-beta.6", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@commitlint/types": "^19.8.1", "@julr/vite-plugin-validate-env": "^2.1.0", "@playwright/test": "^1.53.1", "@tailwindcss/typography": "^0.5.16", "@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.2.0", "@types/react": "^19.1.7", "@types/react-dom": "^19.1.6", "@vite-pwa/assets-generator": "^1.0.0", "@vitejs/plugin-react": "^4.5.2", "commit-and-tag-version": "^12.5.1", "husky": "^9.1.7", "jsdom": "^26.0.0", "lint-staged": "^16.1.0", "tw-animate-css": "^1.3.4", "typescript": "^5.7.2", "vite": "^6.1.0", "vite-plugin-checker": "^0.9.3", "vite-plugin-pwa": "^1.0.0", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.3", "web-vitals": "^5.0.2"}}